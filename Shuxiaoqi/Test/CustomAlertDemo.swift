//
//  CustomAlertDemo.swift
//  Shuxiaoqi
//
//  Created by AI on 2025/7/28.
//

import UIKit

/// 自定义弹窗样式演示
class CustomAlertDemo {
    
    /// 演示删除评论的自定义弹窗样式
    static func showDeleteCommentAlert(in viewController: UIViewController, commentId: Int) {
        let alertView = CommonAlertView(
            title: "确认删除",
            message: "确定后将删除该评论，此操作不可撤销",
            leftButtonTitle: "取消",
            rightButtonTitle: "确认"
        )
        
        // 取消按钮回调
        alertView.onLeftButtonTap = { [weak alertView] in
            print("用户点击了取消按钮")
            alertView?.dismiss()
        }
        
        // 确认按钮回调
        alertView.onRightButtonTap = { [weak alertView] in
            print("用户点击了确认按钮，开始删除评论ID: \(commentId)")
            alertView?.dismiss()
            
            // 这里会调用实际的删除逻辑
            // performDeleteComment(commentId: commentId)
        }
        
        // 显示弹窗
        alertView.show()
    }
    
    /// 弹窗样式说明
    static func getAlertStyleDescription() -> String {
        return """
        自定义删除弹窗样式特点：
        
        📱 弹窗容器：
        • 白色背景
        • 圆角16pt
        • 宽度279pt
        • 居中显示
        • 半透明黑色遮罩背景
        
        📝 文字样式：
        • 标题：17pt粗体，黑色
        • 消息：15pt常规，#666666灰色
        • 居中对齐，支持多行
        
        🔘 按钮样式：
        • 左按钮（取消）：灰色背景#E5E5E5，灰色文字#7E7E7E
        • 右按钮（确认）：橙色背景#FF8F1F，白色文字
        • 圆角17.5pt，高度35pt
        • 按钮间距12pt
        
        ✨ 动画效果：
        • 显示：缩放+淡入动画，0.22秒
        • 隐藏：淡出动画，0.18秒
        
        这个样式与图片中的设计完全一致！
        """
    }
}
