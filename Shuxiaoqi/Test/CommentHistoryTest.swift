//
//  CommentHistoryTest.swift
//  Shuxiaoqi
//
//  Created by AI on 2025/7/28.
//

import Foundation

// 测试评论历史数据映射的示例代码
class CommentHistoryTest {
    
    // 模拟实际API响应的JSON数据
    static let mockAPIResponse = """
    {
        "status": 200,
        "errMsg": "",
        "data": {
            "pageSize": 40,
            "pageNum": 0,
            "total": 25,
            "list": [
                {
                    "createTime": "2025-07-28 09:59:10",
                    "updateTime": "2025-07-28 09:59:10",
                    "id": 902,
                    "commentatorName": "测试1234",
                    "commentatorAvatar": "https://test-image.gzyoushu.com/9fd7aa1a12d64328b64c11a0e1ad4c01.jpg",
                    "commentDesc": "😂😂",
                    "commentImg": null,
                    "worksInfo": null,
                    "commentUserVo": null,
                    "likeNumber": 0,
                    "mentionedUser": null
                },
                {
                    "createTime": "2025-07-26 16:53:02",
                    "updateTime": "2025-07-26 16:53:02",
                    "id": 853,
                    "commentatorName": "测试1234",
                    "commentatorAvatar": "https://test-image.gzyoushu.com/9fd7aa1a12d64328b64c11a0e1ad4c01.jpg",
                    "commentDesc": "🤣🤣",
                    "commentImg": null,
                    "worksInfo": {
                        "createTime": "2025-07-11 15:42:09",
                        "updateTime": "2025-07-25 14:17:23",
                        "id": 175,
                        "worksTitle": "送你只海鸥 从明天起自由",
                        "worksCoverImg": "https://sxq-vod.gzyoushu.com/6cd1aefcvodcq1500037721/5c5118ea5145403691515187758/5145403691502200301.jpg",
                        "labels": [],
                        "watchNumber": 216,
                        "likeNumber": 9659,
                        "commentNumber": 35,
                        "collectNumber": 1,
                        "state": 2,
                        "deleted": 0
                    },
                    "commentUserVo": null,
                    "likeNumber": 0,
                    "mentionedUser": null
                }
            ]
        }
    }
    """
    
    // 测试数据映射功能
    static func testDataMapping() {
        print("=== 开始测试评论历史数据映射 ===")
        
        guard let jsonData = mockAPIResponse.data(using: .utf8) else {
            print("❌ JSON数据转换失败")
            return
        }
        
        do {
            let response = try JSONDecoder().decode(CommentListResponse.self, from: jsonData)
            print("✅ JSON解析成功")
            print("状态码: \(response.status)")
            print("总数: \(response.data.total)")
            print("列表数量: \(response.data.list.count)")
            
            // 测试数据映射
            for (index, item) in response.data.list.enumerated() {
                let historyModel = item.toHistoryModel()
                print("\n--- 评论 \(index + 1) ---")
                print("原始评论ID: \(item.id)")
                print("原始worksId: \(item.worksId)")
                print("worksInfo中的id: \(item.worksInfo?.id ?? -1)")
                print("映射后的评论ID: \(historyModel.commentId)")
                print("映射后的视频ID: \(historyModel.worksId)") // 这个应该是worksInfo.id
                print("评论内容: \(historyModel.comment)")
                print("评论时间: \(historyModel.time)")
                print("视频标题: \(historyModel.title)")
                print("封面图片: \(historyModel.coverImage)")
                print("点赞数: \(historyModel.likes ?? 0)")
                print("观看数: \(historyModel.views ?? 0)")
                print("评论数: \(historyModel.comments ?? 0)")
                print("收藏数: \(historyModel.collects ?? 0)")
                print("可删除: \(historyModel.deleteEnabled)") // 这个应该是true

                if let reply = historyModel.replies.first {
                    print("回复用户: \(reply.username)")
                    print("回复头像: \(reply.avatar)")
                    print("回复内容: \(reply.content)")
                }
            }
            
            print("\n✅ 数据映射测试完成")
            
        } catch {
            print("❌ JSON解析失败: \(error)")
        }
    }
}
