import SmartCodable
import Foundation

// MARK: - Comment List Models

/// 评论用户信息
struct CommentUserVO: SmartCodable {
    var customerId: String = ""
    var nickName: String = ""
    var wxAvator: String = ""
    var state: Int? = nil
}

/// 单条评论信息
struct CommentItem: SmartCodable {
    var createTime: String = ""
    var updateTime: String? = nil
    var id: Int = 0

    // 对应接口新增 / 调整字段
    var worksId: Int = 0
    var commentUserVo: CommentUserVO? = nil
    var pcommentUserVo: CommentUserVO? = nil

    var isMyComment: Bool = false
    var pid: Int = 0
    var commentDesc: String = ""
    @SmartAny var mentionedUser: [String: Any]? = nil // 使用 SmartAny 支持任意字典
    var commentImg: String? = nil // 接口返回字符串形式的数组，如需更细化解析可再处理
    var up: Int = 0
    var likeNumber: Int = 0
    var notLikeNumber: Int = 0
    var address: String? = nil
    var likeState: Int = 0
    var notLikeState: Int = 0
    var reportState: Int = 0
    var isHasChild: Bool = false
    var childCount: Int = 0

    // 新增: 是否为作品作者评论（接口新增字段）
    var isCommentCustomerWorks: Bool = false

    // 兼容旧逻辑，保留 worksInfo
    var worksInfo: WorksInfo? = nil

    // 新增：评论历史接口专用字段（根据实际API响应添加）
    var commentatorName: String? = nil
    var commentatorAvatar: String? = nil

    // MARK: - 自定义解码，处理实际API响应的字段映射
    private enum CodingKeys: String, CodingKey {
        case createTime, updateTime, id, worksId, commentUserVo, pcommentUserVo
        case isMyComment, pid, commentDesc, mentionedUser, commentImg, up
        case likeNumber, notLikeNumber, address, likeState, notLikeState
        case reportState, isHasChild, childCount, isCommentCustomerWorks, worksInfo
        case commentatorName, commentatorAvatar
    }

    init() {}

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 基础字段
        createTime = try container.decodeIfPresent(String.self, forKey: .createTime) ?? ""
        updateTime = try container.decodeIfPresent(String.self, forKey: .updateTime)
        id = try container.decodeIfPresent(Int.self, forKey: .id) ?? 0
        worksId = try container.decodeIfPresent(Int.self, forKey: .worksId) ?? 0

        // 用户信息
        commentUserVo = try container.decodeIfPresent(CommentUserVO.self, forKey: .commentUserVo)
        pcommentUserVo = try container.decodeIfPresent(CommentUserVO.self, forKey: .pcommentUserVo)

        // 评论相关
        isMyComment = try container.decodeIfPresent(Bool.self, forKey: .isMyComment) ?? false
        pid = try container.decodeIfPresent(Int.self, forKey: .pid) ?? 0
        commentDesc = try container.decodeIfPresent(String.self, forKey: .commentDesc) ?? ""

        // 处理 mentionedUser 字段
        if let smartAny = try container.decodeIfPresent(SmartAny<[String: Any]?>.self, forKey: .mentionedUser) {
            _mentionedUser = smartAny
        }

        commentImg = try container.decodeIfPresent(String.self, forKey: .commentImg)
        up = try container.decodeIfPresent(Int.self, forKey: .up) ?? 0

        // 数值字段
        likeNumber = try container.decodeIfPresent(Int.self, forKey: .likeNumber) ?? 0
        notLikeNumber = try container.decodeIfPresent(Int.self, forKey: .notLikeNumber) ?? 0
        likeState = try container.decodeIfPresent(Int.self, forKey: .likeState) ?? 0
        notLikeState = try container.decodeIfPresent(Int.self, forKey: .notLikeState) ?? 0
        reportState = try container.decodeIfPresent(Int.self, forKey: .reportState) ?? 0

        // 其他字段
        address = try container.decodeIfPresent(String.self, forKey: .address)
        isHasChild = try container.decodeIfPresent(Bool.self, forKey: .isHasChild) ?? false
        childCount = try container.decodeIfPresent(Int.self, forKey: .childCount) ?? 0
        isCommentCustomerWorks = try container.decodeIfPresent(Bool.self, forKey: .isCommentCustomerWorks) ?? false

        // 作品信息
        worksInfo = try container.decodeIfPresent(WorksInfo.self, forKey: .worksInfo)

        // 评论历史专用字段
        commentatorName = try container.decodeIfPresent(String.self, forKey: .commentatorName)
        commentatorAvatar = try container.decodeIfPresent(String.self, forKey: .commentatorAvatar)
    }

    /// 将网络模型映射为旧的 `CommentHistoryModel` 供 UI 复用
    func toHistoryModel() -> CommentHistoryModel {
        let cover = worksInfo?.worksCoverImg ?? "comment_cover" // 占位图
        let title = worksInfo?.worksTitle ?? ""
        let tags = worksInfo?.labels ?? []

        // 优先使用评论历史接口的专用字段，如果没有则使用commentUserVo
        let avatar = commentatorAvatar ?? commentUserVo?.wxAvator ?? "default_avatar"
        let username = commentatorName ?? commentUserVo?.nickName ?? "匿名用户"

        let reply = ReplyModel(
            avatar: avatar,
            username: username,
            content: commentDesc,
            time: createTime,
            replyToUser: pcommentUserVo?.nickName,
            replyToContent: nil,
            isLiked: likeState == 1
        )
        return CommentHistoryModel(
            commentId: self.id,
            worksId: self.worksId,
            coverImage: cover,
            title: title,
            tags: tags,
            comment: commentDesc,
            time: createTime,
            likes: worksInfo?.likeNumber ?? likeNumber, // 优先使用作品的点赞数
            views: worksInfo?.watchNumber,
            comments: worksInfo?.commentNumber,
            collects: worksInfo?.collectNumber,
            replies: [reply],
            deleteEnabled: isMyComment
        )
    }
}

/// 作品信息（可选）
struct WorksInfo: SmartCodable {
    var createTime: String? = nil
    var updateTime: String? = nil
    var id: Int? = nil
    var worksTitle: String? = nil
    var worksCoverImg: String? = nil
    var labels: [String]? = nil
    var watchNumber: Int? = nil
    var likeNumber: Int? = nil
    var commentNumber: Int? = nil
    var collectNumber: Int? = nil
    var state: Int? = nil
    var deleted: Int? = nil
}

/// 分页数据
struct CommentPage: SmartCodable {
    var pageSize: Int = 0
    var pageNum: Int = 0
    var total: Int = 0
    /// 为兼容旧接口(list)与新接口(comment)，统一映射到 list 属性
    var list: [CommentItem] = []
    @SmartAny var mentionedUser: [String: Any]? = nil // 使用 SmartAny
    var empty: Bool = false

    // MARK: - 自定义解码，优先使用 comment 字段
    private enum CodingKeys: String, CodingKey {
        case pageSize, pageNum, total, list, comment, empty, mentionedUser
    }

    init() {}

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        pageSize = try container.decodeIfPresent(Int.self, forKey: .pageSize) ?? 0
        pageNum  = try container.decodeIfPresent(Int.self, forKey: .pageNum)  ?? 0
        total    = try container.decodeIfPresent(Int.self, forKey: .total)    ?? 0

        // 兼容字段: comment(新) 或 list(旧)
        if let commentArr = try container.decodeIfPresent([CommentItem].self, forKey: .comment) {
            list = commentArr
        } else {
            list = try container.decodeIfPresent([CommentItem].self, forKey: .list) ?? []
        }

        empty = try container.decodeIfPresent(Bool.self, forKey: .empty) ?? false

        // 解析 mentionedUser 映射表（可选）
        if let smartAny = try container.decodeIfPresent(SmartAny<[String: Any]?>.self, forKey: .mentionedUser) {
            _mentionedUser = smartAny
        }
    }

    // Encodable 实现，保持字段一致
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(pageSize, forKey: .pageSize)
        try container.encode(pageNum,  forKey: .pageNum)
        try container.encode(total,    forKey: .total)
        // 统一编码为 comment 字段，便于后端解析
        try container.encode(list, forKey: .comment)
        try container.encode(empty, forKey: .empty)
        try container.encode(_mentionedUser, forKey: .mentionedUser)
    }
}

/// 评论列表响应
struct CommentListResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var data: CommentPage = .init()
    var msg: String = ""
    
    var isSuccess: Bool { status == 200 }

    private enum CodingKeys: String, CodingKey {
        case status, errMsg, data, msg
    }

    init() {}

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        status = try container.decodeIfPresent(Int.self, forKey: .status) ?? 0
        errMsg = try container.decodeIfPresent(String.self, forKey: .errMsg) ?? ""
        msg    = try container.decodeIfPresent(String.self, forKey: .msg)    ?? ""

        // 兼容两种 data 结构：纯数组 或 分页对象
        // 先尝试解析为数组（新接口），若为空再尝试分页对象（旧接口）
        if let list = try? container.decode([CommentItem].self, forKey: .data), !list.isEmpty {
            var page = CommentPage()
            page.list = list
            page.total = list.count
            page.empty = list.isEmpty
            data = page
        } else if let page = try? container.decode(CommentPage.self, forKey: .data) {
            data = page
        } else {
            data = CommentPage()
        }
    }
}

struct CommentSingleResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var data: CommentItem = .init()
    var msg: String = ""
    var isSuccess: Bool { status == 200 }
}

/*
{
	"status": 200,
	"errMsg": "",
	"data": {
		"comment": [
			{
				"createTime": "2025-07-12 15:51:49",
				"updateTime": "2025-07-12 15:51:49",
				"id": 226,
				"worksId": 175,
				"commentUserVo": {
					"customerId": "40288ae8966fc07d01966fc2f95d0000",
					"nickName": "迪士尼在逃公主",
					"wxAvator": "https://image.gzyoushu.com/03DLP44C6UL6AM96.png",
					"state": null
				},
				"isMyComment": false,
				"pid": null,
				"commentDesc": "111",
				"mentionedUser": null,
				"commentImg": "[]",
				"up": 0,
				"likeNumber": 0,
				"notLikeNumber": 0,
				"address": "广东省广州市天河区光大银行大厦",
				"likeState": 0,
				"notLikeState": 0,
				"reportState": 0,
				"isHasChild": false,
				"childCount": 0,
				"pcommentUserVo": null
			},
			{
				"createTime": "2025-07-12 15:51:39",
				"updateTime": "2025-07-12 15:51:39",
				"id": 225,
				"worksId": 175,
				"commentUserVo": {
					"customerId": "40288ae8966fc07d01966fc2f95d0000",
					"nickName": "迪士尼在逃公主",
					"wxAvator": "https://image.gzyoushu.com/03DLP44C6UL6AM96.png",
					"state": null
				},
				"isMyComment": false,
				"pid": null,
				"commentDesc": "测速",
				"mentionedUser": null,
				"commentImg": "[]",
				"up": 0,
				"likeNumber": 0,
				"notLikeNumber": 0,
				"address": "广东省广州市天河区光大银行大厦",
				"likeState": 0,
				"notLikeState": 0,
				"reportState": 0,
				"isHasChild": true,
				"childCount": 3,
				"pcommentUserVo": null
			},
			{
				"createTime": "2025-07-12 15:40:37",
				"updateTime": "2025-07-12 15:40:37",
				"id": 219,
				"worksId": 175,
				"commentUserVo": {
					"customerId": "40288ae8966fc07d01966fc2f95d0000",
					"nickName": "迪士尼在逃公主",
					"wxAvator": "https://image.gzyoushu.com/03DLP44C6UL6AM96.png",
					"state": null
				},
				"isMyComment": false,
				"pid": null,
				"commentDesc": "哈哈哈",
				"mentionedUser": null,
				"commentImg": "[]",
				"up": 0,
				"likeNumber": 0,
				"notLikeNumber": 0,
				"address": "广东省广州市天河区光大银行大厦",
				"likeState": 0,
				"notLikeState": 0,
				"reportState": 0,
				"isHasChild": false,
				"childCount": 0,
				"pcommentUserVo": null
			},
			{
				"createTime": "2025-07-12 15:31:05",
				"updateTime": "2025-07-12 15:31:05",
				"id": 216,
				"worksId": 175,
				"commentUserVo": {
					"customerId": "40288ae8966fc07d01966fc2f95d0000",
					"nickName": "迪士尼在逃公主",
					"wxAvator": "https://image.gzyoushu.com/03DLP44C6UL6AM96.png",
					"state": null
				},
				"isMyComment": false,
				"pid": null,
				"commentDesc": "嘻嘻",
				"mentionedUser": null,
				"commentImg": "[]",
				"up": 0,
				"likeNumber": 0,
				"notLikeNumber": 0,
				"address": "广东省广州市天河区光大银行大厦",
				"likeState": 0,
				"notLikeState": 0,
				"reportState": 0,
				"isHasChild": false,
				"childCount": 0,
				"pcommentUserVo": null
			},
			{
				"createTime": "2025-07-11 16:27:30",
				"updateTime": "2025-07-11 16:27:30",
				"id": 153,
				"worksId": 175,
				"commentUserVo": {
					"customerId": "ff808081967a49ca01967a7cdf1a0000",
					"nickName": "测试1234",
					"wxAvator": "https://test-image.gzyoushu.com/9fd7aa1a12d64328b64c11a0e1ad4c01.jpg",
					"state": null
				},
				"isMyComment": true,
				"pid": null,
				"commentDesc": "不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟",
				"mentionedUser": null,
				"commentImg": "[\"https://test-image.gzyoushu.com/9fd7aa1a12d64328b64c11a0e1ad4c01.jpg\"]",
				"up": 0,
				"likeNumber": 0,
				"notLikeNumber": 0,
				"address": "中国广东广州",
				"likeState": 0,
				"notLikeState": 0,
				"reportState": 0,
				"isHasChild": true,
				"childCount": 1,
				"pcommentUserVo": null
			},
			{
				"createTime": "2025-07-11 16:07:21",
				"updateTime": "2025-07-11 16:07:21",
				"id": 148,
				"worksId": 175,
				"commentUserVo": {
					"customerId": "ff80808197e991ec0197e9d3fc5d0000",
					"nickName": "The way",
					"wxAvator": "https://test-image.gzyoushu.com/1a3e0b91c9fd4180bd673eda8c8bed9c.jpg",
					"state": null
				},
				"isMyComment": false,
				"pid": null,
				"commentDesc": "@ff8080819674cbc0019674d1594b0000\\谢谢",
				"mentionedUser": null,
				"commentImg": "[]",
				"up": 0,
				"likeNumber": 0,
				"notLikeNumber": 1,
				"address": "广东省广州市天河区光大银行大厦",
				"likeState": 0,
				"notLikeState": 0,
				"reportState": 0,
				"isHasChild": false,
				"childCount": 0,
				"pcommentUserVo": null
			},
			{
				"createTime": "2025-07-11 16:07:18",
				"updateTime": "2025-07-11 16:07:18",
				"id": 147,
				"worksId": 175,
				"commentUserVo": {
					"customerId": "ff80808197e991ec0197e9d3fc5d0000",
					"nickName": "The way",
					"wxAvator": "https://test-image.gzyoushu.com/1a3e0b91c9fd4180bd673eda8c8bed9c.jpg",
					"state": null
				},
				"isMyComment": false,
				"pid": null,
				"commentDesc": "@ff8080819674cbc0019674d1594b0000\\谢谢",
				"mentionedUser": null,
				"commentImg": "[]",
				"up": 0,
				"likeNumber": 0,
				"notLikeNumber": 0,
				"address": "广东省广州市天河区光大银行大厦",
				"likeState": 0,
				"notLikeState": 0,
				"reportState": 0,
				"isHasChild": false,
				"childCount": 0,
				"pcommentUserVo": null
			},
			{
				"createTime": "2025-07-11 15:51:47",
				"updateTime": "2025-07-11 15:51:47",
				"id": 142,
				"worksId": 175,
				"commentUserVo": {
					"customerId": "ff808081967a49ca01967a7cdf1a0000",
					"nickName": "测试1234",
					"wxAvator": "https://test-image.gzyoushu.com/9fd7aa1a12d64328b64c11a0e1ad4c01.jpg",
					"state": null
				},
				"isMyComment": true,
				"pid": null,
				"commentDesc": "不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟，不错不错，有新的感1悟",
				"mentionedUser": null,
				"commentImg": null,
				"up": 0,
				"likeNumber": 0,
				"notLikeNumber": 0,
				"address": "中国广东广州",
				"likeState": 0,
				"notLikeState": 0,
				"reportState": 0,
				"isHasChild": true,
				"childCount": 4,
				"pcommentUserVo": null
			},
			{
				"createTime": "2025-07-11 15:48:56",
				"updateTime": "2025-07-11 15:48:56",
				"id": 141,
				"worksId": 175,
				"commentUserVo": {
					"customerId": "ff808081967a49ca01967a7cdf1a0000",
					"nickName": "测试1234",
					"wxAvator": "https://test-image.gzyoushu.com/9fd7aa1a12d64328b64c11a0e1ad4c01.jpg",
					"state": null
				},
				"isMyComment": true,
				"pid": null,
				"commentDesc": "不错不错，有新的感1悟",
				"mentionedUser": null,
				"commentImg": null,
				"up": 0,
				"likeNumber": 0,
				"notLikeNumber": 0,
				"address": "中国广东广州",
				"likeState": 0,
				"notLikeState": 0,
				"reportState": 0,
				"isHasChild": false,
				"childCount": 0,
				"pcommentUserVo": null
			},
			{
				"createTime": "2025-07-11 15:48:45",
				"updateTime": "2025-07-11 15:48:45",
				"id": 140,
				"worksId": 175,
				"commentUserVo": {
					"customerId": "ff808081967a49ca01967a7cdf1a0000",
					"nickName": "测试1234",
					"wxAvator": "https://test-image.gzyoushu.com/9fd7aa1a12d64328b64c11a0e1ad4c01.jpg",
					"state": null
				},
				"isMyComment": true,
				"pid": null,
				"commentDesc": "不错不错，有新的感1悟",
				"mentionedUser": null,
				"commentImg": null,
				"up": 0,
				"likeNumber": 0,
				"notLikeNumber": 0,
				"address": "中国广东广州",
				"likeState": 0,
				"notLikeState": 0,
				"reportState": 0,
				"isHasChild": false,
				"childCount": 0,
				"pcommentUserVo": null
			}
		],
		"mentionedUser": {
			"ff8080819674cbc0019674d1594b0000": "兔蛮蛮"
		}
	},
	"msg": "成功"
}
 
 
二级api请求
 {
     "status": 200,
     "errMsg": "",
     "data": {
         "pageSize": 10,
         "pageNum": 0,
         "total": 3,
         "list": [
             {
                 "createTime": "2025-07-10 11:29:34",
                 "updateTime": "2025-07-10 11:29:34",
                 "id": 92,
                 "worksId": 12,
                 "commentUserVo": {
                     "customerId": "ff808081967a083e01967a17435a0000",
                     "nickName": "优树用户GQg60612",
                     "wxAvator": "http://test-image.gzyoushu.com/9799bd296c2b4be19ab5830acca91ff8.jpg",
                     "state": null
                 },
                 "isMyComment": false,
                 "pid": 60,
                 "commentDesc": "丰田",
                 "mentionedUser": null,
                 "commentImg": "[]",
                 "up": 0,
                 "likeNumber": 0,
                 "notLikeNumber": 0,
                 "address": "广东省广州市天河区光大银行大厦",
                 "likeState": 0,
                 "notLikeState": 0,
                 "reportState": 0,
                 "isHasChild": false,
                 "childCount": 0,
                 "pcommentUserVo": null
             },
             {
                 "createTime": "2025-07-10 11:07:10",
                 "updateTime": "2025-07-10 11:07:10",
                 "id": 86,
                 "worksId": 12,
                 "commentUserVo": {
                     "customerId": "ff808081967a083e01967a17435a0000",
                     "nickName": "优树用户GQg60612",
                     "wxAvator": "http://test-image.gzyoushu.com/9799bd296c2b4be19ab5830acca91ff8.jpg",
                     "state": null
                 },
                 "isMyComment": false,
                 "pid": 60,
                 "commentDesc": "工业园",
                 "mentionedUser": null,
                 "commentImg": "[]",
                 "up": 0,
                 "likeNumber": 0,
                 "notLikeNumber": 0,
                 "address": "广东省广州市天河区光大银行大厦",
                 "likeState": 0,
                 "notLikeState": 0,
                 "reportState": 0,
                 "isHasChild": false,
                 "childCount": 0,
                 "pcommentUserVo": {
                     "customerId": "ff80808197e991ec0197e9d3fc5d0000",
                     "nickName": "优树用户12a330",
                     "wxAvator": "https://image.gzyoushu.com/03DLP44C6UL6AM96.png",
                     "state": null
                 }
             },
             {
                 "createTime": "2025-07-10 10:39:50",
                 "updateTime": "2025-07-10 10:39:50",
                 "id": 85,
                 "worksId": 12,
                 "commentUserVo": {
                     "customerId": "ff80808197e991ec0197e9d3fc5d0000",
                     "nickName": "优树用户12a330",
                     "wxAvator": "https://image.gzyoushu.com/03DLP44C6UL6AM96.png",
                     "state": null
                 },
                 "isMyComment": false,
                 "pid": 60,
                 "commentDesc": "ヽ(○^㉨^)ﾉ♪",
                 "mentionedUser": null,
                 "commentImg": "[]",
                 "up": 0,
                 "likeNumber": 0,
                 "notLikeNumber": 0,
                 "address": "广东省广州市天河区光大银行大厦",
                 "likeState": 0,
                 "notLikeState": 0,
                 "reportState": 0,
                 "isHasChild": false,
                 "childCount": 0,
                 "pcommentUserVo": null
             }
         ],
         "mentionedUser": {},
         "empty": false
     },
     "msg": "成功"
 }
*/

/*
 修改后的一级api请求
 
 {
     "status": 200,
     "errMsg": "",
     "data": [
         {
             "createTime": "2025-07-14 11:58:24",
             "updateTime": "2025-07-14 11:58:24",
             "id": 302,
             "worksId": 175,
             "commentUserVo": {
                 "customerId": "40288ae8966fc07d01966fc2f95d0000",
                 "nickName": "迪士尼在逃公主",
                 "wxAvator": "https://image.gzyoushu.com/03DLP44C6UL6AM96.png",
                 "state": null
             },
             "isMyComment": false,
             "isCommentCustomerWorks": false,
             "pid": null,
             "commentDesc": "哈哈哈哈哈",
             "mentionedUser": null,
             "commentImg": "[]",
             "up": 0,
             "likeNumber": 0,
             "notLikeNumber": 0,
             "address": "广东省广州市天河区光大银行大厦",
             "likeState": 0,
             "notLikeState": 0,
             "reportState": 0,
             "isHasChild": false,
             "childCount": 0,
             "pcommentUserVo": null
         },
         {
             "createTime": "2025-07-14 11:39:09",
             "updateTime": "2025-07-14 11:39:09",
             "id": 288,
             "worksId": 175,
             "commentUserVo": {
                 "customerId": "ff8080819806e9ed0198070103c70001",
                 "nickName": "优树用户05us94",
                 "wxAvator": "https://image.gzyoushu.com/03DLP44C6UL6AM96.png",
                 "state": null
             },
             "isMyComment": false,
             "isCommentCustomerWorks": false,
             "pid": null,
             "commentDesc": "哈哈哈哈",
             "mentionedUser": null,
             "commentImg": "[]",
             "up": 0,
             "likeNumber": 0,
             "notLikeNumber": 0,
             "address": "广东省广州市天河区光大银行大厦",
             "likeState": 0,
             "notLikeState": 0,
             "reportState": 0,
             "isHasChild": true,
             "childCount": 3,
             "pcommentUserVo": null
         },
         {
             "createTime": "2025-07-14 09:44:53",
             "updateTime": "2025-07-14 09:44:53",
             "id": 277,
             "worksId": 175,
             "commentUserVo": {
                 "customerId": "ff80808197eef3ae0197ef04f03b0000",
                 "nickName": "k know",
                 "wxAvator": "https://image.gzyoushu.com/03DLP44C6UL6AM96.png",
                 "state": null
             },
             "isMyComment": false,
             "isCommentCustomerWorks": false,
             "pid": null,
             "commentDesc": "@ff80808197ed39c90197ee2bdf7d0000\\",
             "mentionedUser": {
                 "ff80808197ed39c90197ee2bdf7d0000": "25667"
             },
             "commentImg": "[]",
             "up": 0,
             "likeNumber": 0,
             "notLikeNumber": 0,
             "address": "中国广东广州",
             "likeState": 0,
             "notLikeState": 0,
             "reportState": 0,
             "isHasChild": true,
             "childCount": 3,
             "pcommentUserVo": null
         },
         {
             "createTime": "2025-07-14 09:42:24",
             "updateTime": "2025-07-14 09:42:24",
             "id": 272,
             "worksId": 175,
             "commentUserVo": {
                 "customerId": "ff80808197eef3ae0197ef04f03b0000",
                 "nickName": "k know",
                 "wxAvator": "https://image.gzyoushu.com/03DLP44C6UL6AM96.png",
                 "state": null
             },
             "isMyComment": false,
             "isCommentCustomerWorks": false,
             "pid": null,
             "commentDesc": " 是否",
             "mentionedUser": null,
             "commentImg": "[\"https://test-image.gzyoushu.com/a5b6de56d780418aa111e025e034f842.jpg\"]",
             "up": 0,
             "likeNumber": 0,
             "notLikeNumber": 0,
             "address": "中国广东广州",
             "likeState": 0,
             "notLikeState": 0,
             "reportState": 0,
             "isHasChild": true,
             "childCount": 1,
             "pcommentUserVo": null
         }
     ],
     "msg": "成功"
 }
 
 
 
 */
