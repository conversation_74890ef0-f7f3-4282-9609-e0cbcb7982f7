//
//  CommentHistoryModel.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/3/25.
//

import Foundation

// MARK: - Model
struct CommentHistoryModel {
    let commentId: Int      // 新增，评论id
    let worksId: Int        // 新增，视频id
    let coverImage: String
    let title: String
    let tags: [String]  // 修改为数组支持多个标签
    let comment: String
    let time: String
    let likes: Int?     // 点赞数，可空
    let views: Int?     // 浏览数
    let comments: Int?  // 评论数
    let collects: Int?  // 收藏数
    let replies: [ReplyModel]  // 添加回复列表
    let deleteEnabled: Bool    // 是否显示删除按钮
    
//    static func mockData() -> CommentHistoryModel {
//        return CommentHistoryModel(
//            commentId: 1, // mock id
//            worksId: 1,   // mock worksId
//            coverImage: "comment_cover",
//            title: "街舞少年的完美展现街舞少年的完美展现",
//            tags: ["舞蹈", "教学"],
//            comment: "这个舞蹈跳得太棒了！节奏感很强，舞步也很优美。希望能看到更多类似的作品，给你点赞！",
//            time: "评论于2025-3-7",
//            likes: 99,
//            views: 99,
//            comments: 99,
//            collects: 0,
//            replies: [
//                ReplyModel(avatar: "default_avatar", username: "用户名称", content: "同感！动作编排很用心", time: "12:32", replyToUser: nil, replyToContent: nil, isLiked: false),
//                ReplyModel(avatar: "default_avatar", username: "用户名称", content: "同感！动作编排很用心", time: "12:32", replyToUser: nil, replyToContent: nil, isLiked: false),
//                ReplyModel(avatar: "default_avatar", username: "用户名称", content: "同感！动作编排很用心", time: "12:32", replyToUser: nil, replyToContent: nil, isLiked: false),
//                ReplyModel(avatar: "default_avatar", username: "用户名称", content: "同感！动作编排很用心", time: "12:32", replyToUser: nil, replyToContent: nil, isLiked: false)
//            ],
//            deleteEnabled: true
//        )
//    }
//    
//    static func mockList() -> [CommentHistoryModel] {
//        return [
//            // 一级评论
//            CommentHistoryModel(
//                commentId: 1,
//                worksId: 1,
//                coverImage: "comment_cover",
//                title: "街舞少年的完美展现街舞少年的完美展现",
//                tags: ["舞蹈", "教学"],
//                comment: "我觉得很棒！",
//                time: "2025-3-7 10:00",
//                likes: 99,
//                views: 99,
//                comments: 99,
//                replies: [
//                    ReplyModel(
//                        avatar: "default_avatar",
//                        username: "再睡五分钟",
//                        content: "我觉得很棒！",
//                        time: "2025-3-7 10:00",
//                        replyToUser: nil,
//                        replyToContent: nil,
//                        isLiked: true
//                    )
//                ],
//                deleteEnabled: true
//            ),
//            // 二级评论
//            CommentHistoryModel(
//                commentId: 2,
//                worksId: 2,
//                coverImage: "comment_cover2",
//                title: "音乐律动",
//                tags: ["音乐"],
//                comment: "节奏感很强，舞姿也很优美！",
//                time: "2025-3-8 09:30",
//                likes: 88,
//                views: 88,
//                comments: 88,
//                replies: [
//                    ReplyModel(
//                        avatar: "default_avatar",
//                        username: "再睡五分钟",
//                        content: "节奏感很强，舞姿也很优美！",
//                        time: "2025-3-8 09:30",
//                        replyToUser: "伸个懒腰",
//                        replyToContent: "同感！动作编排很用心",
//                        isLiked: false
//                    )
//                ],
//                deleteEnabled: true
//            )
//        ]
//    }
}
