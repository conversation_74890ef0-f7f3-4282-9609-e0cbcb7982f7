//
//  CommentHistoryViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/3/25.
//

import UIKit
import SnapKit

// 首先添加一个代理协议
protocol CommentHistoryCellDelegate: AnyObject {
    func commentHistoryCell(_ cell: CommentHistoryCell, didChangeExpanded isExpanded: Bool)
    func commentHistoryCellDidTapDelete(_ cell: CommentHistoryCell, commentId: Int)
    func commentHistoryCellDidTapCell(_ cell: CommentHistoryCell, commentId: Int, worksId: Int)
}

class CommentHistoryViewController: BaseViewController {
    
    // MARK: - Properties
    //    private let segmentTitles = ["内容", "商品", "店铺"]
    //    private var currentIndex = 0
    
    // MARK: - Data Source
    private var commentList: [CommentHistoryModel] = []
    private var pageNum: Int = 0
    private let pageSize: Int = 40 // 根据API响应调整为40
    private var isLoading: Bool = false
    private var hasMoreData: Bool = true
    
    // 新增：空数据占位图
    private lazy var emptyPlaceholderView: UIView = {
        let view = UIView()
        view.isHidden = true
        
        let imageView = UIImageView(image: UIImage(named: "empty_data_placeholder_image"))
        imageView.contentMode = .scaleAspectFit
        
        let label = UILabel()
        label.text = "没有更多了"
        label.textColor = UIColor(hex: "#999999")
        label.font = UIFont.systemFont(ofSize: 14)
        label.textAlignment = .center
        
        view.addSubview(imageView)
        view.addSubview(label)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-80)
            make.width.height.equalTo(220)
        }
        
        label.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(17)
            make.centerX.equalToSuperview()
        }
        
        return view
    }()
    
    // MARK: - UI Components
    //    private lazy var segmentControl: UISegmentedControl = {
    //        let segment = UISegmentedControl(items: segmentTitles)
    //        segment.selectedSegmentIndex = 0
    //        segment.selectedSegmentTintColor = .white
    //        
    //        // 设置正常状态的文字颜色
    //        let normalTextAttributes = [
    //            NSAttributedString.Key.foregroundColor: UIColor(hex: "#666666"),
    //            NSAttributedString.Key.font: UIFont.systemFont(ofSize: 15)
    //        ]
    //        segment.setTitleTextAttributes(normalTextAttributes, for: .normal)
    //        
    //        // 设置选中状态的文字颜色
    //        let selectedTextAttributes = [
    //            NSAttributedString.Key.foregroundColor: UIColor(hex: "#FF6236"),
    //            NSAttributedString.Key.font: UIFont.systemFont(ofSize: 15)
    //        ]
    //        segment.setTitleTextAttributes(selectedTextAttributes, for: .selected)
    //        
    //        // 设置所有状态的背景色为白色
    //        let normalBgImage = UIImage(color: .white)
    //        segment.setBackgroundImage(normalBgImage, for: .normal, barMetrics: .default)
    //        segment.setBackgroundImage(normalBgImage, for: .selected, barMetrics: .default)
    //        segment.setBackgroundImage(normalBgImage, for: .highlighted, barMetrics: .default)
    //        
    //        // 移除系统默认的分割线
    //        segment.setDividerImage(UIImage(), forLeftSegmentState: .normal, rightSegmentState: .normal, barMetrics: .default)
    //        
    //        segment.addTarget(self, action: #selector(segmentValueChanged(_:)), for: .valueChanged)
    //        return segment
    //    }()
    
    //    private lazy var indicatorView: UIView = {
    //        let view = UIView()
    //        view.backgroundColor = UIColor(hex: "#FF6236")
    //        return view
    //    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.separatorStyle = .none
        table.backgroundColor = UIColor(hex: "#F5F5F5")
        table.estimatedRowHeight = 120
        table.rowHeight = UITableView.automaticDimension
        table.register(CommentHistoryCell.self, forCellReuseIdentifier: "CommentHistoryCell")
        if #available(iOS 15.0, *) {
            table.sectionHeaderTopPadding = 0
        }

        // 添加下拉刷新
        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(self, action: #selector(refreshData), for: .valueChanged)
        table.refreshControl = refreshControl

        return table
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        navTitle = "评论历史"
        setupUI()
        fetchCommentHistory()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        view.backgroundColor = .white
        
        // 添加分段控制器
        //        contentView.addSubview(segmentControl)
        //        segmentControl.snp.makeConstraints { make in
        //            make.top.equalToSuperview()
        //            make.left.right.equalToSuperview()
        //            make.height.equalTo(44)
        //        }
        
        // 修改指示器宽度为按钮宽度
        //        contentView.addSubview(indicatorView)
        //        indicatorView.snp.makeConstraints { make in
        //            make.bottom.equalTo(segmentControl)
        //            make.height.equalTo(2)
        //            make.width.equalTo(UIScreen.main.bounds.width / CGFloat(segmentTitles.count)) // 设置为按钮宽度
        //            make.centerX.equalTo(segmentControl.snp.left).offset(UIScreen.main.bounds.width / CGFloat(segmentTitles.count) / 2)
        //        }
        
        // 添加表格视图
        contentView.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            //            make.top.equalTo(segmentControl.snp.bottom)
            make.top.equalToSuperview()
            make.left.right.bottom.equalToSuperview()
        }
        
        // 添加空数据占位图
        contentView.addSubview(emptyPlaceholderView)
        emptyPlaceholderView.snp.makeConstraints { make in
            make.edges.equalTo(tableView)
        }
    }
    
    // MARK: - Actions
    //    @objc private func segmentValueChanged(_ sender: UISegmentedControl) {
    //        currentIndex = sender.selectedSegmentIndex
    //        
    //        // 更新指示器位置，保持宽度不变
    //        let segmentWidth = UIScreen.main.bounds.width / CGFloat(segmentTitles.count)
    //        let centerX = segmentWidth * CGFloat(currentIndex) + segmentWidth / 2
    //        
    //        UIView.animate(withDuration: 0.3) {
    //            self.indicatorView.snp.updateConstraints { make in
    //                make.centerX.equalTo(self.segmentControl.snp.left).offset(centerX)
    //            }
    //            self.view.layoutIfNeeded()
    //        }
    //        
    //        // 根据不同的分段更新数据
    //        updateDataForCurrentSegment()
    //    }
    
    // 新增：根据当前分段更新数据
    //    private func updateDataForCurrentSegment() {
    //        switch currentIndex {
    //        case 0: // 内容
    //            // 使用mock数据
    //            commentList = CommentHistoryModel.mockList()
    //        case 1, 2: // 商品和店铺
    //            // 显示空状态
    //            commentList = []
    //        default:
    //            commentList = []
    //        }
    //        
    //        // 更新空数据占位图显示状态
    //        emptyPlaceholderView.isHidden = !commentList.isEmpty
    //        tableView.reloadData()
    //    }
    
    // MARK: - Actions
    @objc private func refreshData() {
        pageNum = 0
        hasMoreData = true
        fetchCommentHistory()
    }

    // MARK: - Networking
    private func fetchCommentHistory() {
        guard !isLoading else { return }
        isLoading = true

        APIManager.shared.getCommentList(page: pageNum, size: pageSize) { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isLoading = false
                self.tableView.refreshControl?.endRefreshing()

                switch result {
                case .success(let response):
                    if response.isSuccess {
                        let models = response.data.list.map { $0.toHistoryModel() }

                        if self.pageNum == 0 {
                            self.commentList = models
                        } else {
                            self.commentList.append(contentsOf: models)
                        }

                        // 检查是否还有更多数据
                        self.hasMoreData = models.count >= self.pageSize

                        // 更新页码
                        if !models.isEmpty {
                            self.pageNum += 1
                        }

                    } else {
                        self.showToast(response.errMsg.isEmpty ? "获取评论历史失败" : response.errMsg)
                    }
                case .failure(let error):
                    print("获取评论历史失败：\(error.localizedDescription)")
                    self.showToast("网络请求失败，请稍后重试")
                }

                // 更新 UI
                self.emptyPlaceholderView.isHidden = !self.commentList.isEmpty
                self.tableView.reloadData()
            }
        }
    }
}

// MARK: - UITableViewDelegate & UITableViewDataSource
extension CommentHistoryViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return commentList.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "CommentHistoryCell", for: indexPath) as! CommentHistoryCell
        cell.delegate = self
        cell.configure(with: commentList[indexPath.row])

        // 上拉加载更多
        if indexPath.row == commentList.count - 1 && hasMoreData && !isLoading {
            fetchCommentHistory()
        }

        return cell
    }
}

// MARK: - CommentHistoryCellDelegate
extension CommentHistoryViewController: CommentHistoryCellDelegate {
    func commentHistoryCell(_ cell: CommentHistoryCell, didChangeExpanded isExpanded: Bool) {
        if let indexPath = tableView.indexPath(for: cell) {
            UIView.animate(withDuration: 0.3) {
                self.tableView.performBatchUpdates(nil)
            }
        }
    }
    func commentHistoryCellDidTapDelete(_ cell: CommentHistoryCell, commentId: Int) {
        // 显示确认删除对话框
        let alert = UIAlertController(title: "删除评论", message: "确定要删除这条评论吗？", preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "删除", style: .destructive) { [weak self] _ in
            self?.performDeleteComment(commentId: commentId)
        })

        present(alert, animated: true)
    }

    private func performDeleteComment(commentId: Int) {
        // 调用APIManager删除评论
        APIManager.shared.deleteComment(commentId: commentId) { [weak self] result in
            guard let self = self else { return }

            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        // 删除成功，重新加载第一页数据
                        self.pageNum = 0
                        self.hasMoreData = true
                        self.fetchCommentHistory()
                        self.showToast("删除成功")
                    } else {
                        self.showToast(response.errMsg.isEmpty ? "删除失败" : response.errMsg)
                    }
                case .failure(let error):
                    print("删除评论失败：\(error.localizedDescription)")
                    self.showToast("删除失败，请稍后重试")
                }
            }
        }
    }
    func commentHistoryCellDidTapCell(_ cell: CommentHistoryCell, commentId: Int, worksId: Int) {
        // 跳转到视频详情页
        print("点击cell，评论id: \(commentId)，视频id: \(worksId)")
        navigateToVideoDetail(videoId: worksId)
    }

    // MARK: - Navigation
    private func navigateToVideoDetail(videoId: Int) {
        // 显示加载指示器
        let loadingHUD = UIActivityIndicatorView(style: .large)
        loadingHUD.center = view.center
        view.addSubview(loadingHUD)
        loadingHUD.startAnimating()

        // 根据视频ID获取视频详情
        APIManager.shared.getVideoDetail(videoId: videoId) { [weak self] result in
            DispatchQueue.main.async {
                loadingHUD.removeFromSuperview()
                guard let self = self else { return }

                switch result {
                case .success(let response):
                    guard let videoItem = response.data else {
                        self.showToast("获取视频详情失败")
                        return
                    }

                    // 创建视频播放控制器，单独展示这个视频（不做列表请求）
                    let playerVC = VideoDisplayCenterViewController(
                        videoList: [videoItem],
                        startIndex: 0,
                        hideNavBackButton: false,
                        showCustomNavBar: true,
                        needsTabBarOffset: false
                    )

                    self.navigationController?.pushViewController(playerVC, animated: true)

                case .failure(let error):
                    self.showToast("获取视频详情失败: \(error.localizedDescription)")
                }
            }
        }
    }

    // MARK: - Helper Methods
    private func showToast(_ message: String) {
        // 简单的Toast提示实现
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        present(alert, animated: true)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            alert.dismiss(animated: true)
        }
    }
}

// MARK: - UIImage扩展：生成纯色图片
extension UIImage {
    convenience init(color: UIColor, size: CGSize = CGSize(width: 1, height: 1)) {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        color.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        let image = UIGraphicsGetImageFromCurrentImageContext()!
        UIGraphicsEndImageContext()
        self.init(cgImage: image.cgImage!)
    }
}

