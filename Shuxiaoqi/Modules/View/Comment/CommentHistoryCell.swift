//
//  CommentHistoryCell.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/25.
//

import UIKit
import SnapKit
import Kingfisher

// MARK: - CommentHistoryCell
class CommentHistoryCell: UITableViewCell {
    
    // MARK: - Properties
    weak var delegate: CommentHistoryCellDelegate?
    private var commentLikeIconTopConstraint: Constraint? = nil
    var commentId: Int = 0
    var worksId: Int = 0
    
    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 8
        return view
    }()
    
    // 视频封面
    private let coverImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.backgroundColor = UIColor(hex: "#F5F5F5")
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 4
        return imageView
    }()
    // 视频标题
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = UIColor(hex: "#333333")
        label.numberOfLines = 1
        return label
    }()
    // 标签
    private let tagStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8
        stackView.alignment = .leading
        return stackView
    }()
    // 点赞（heart）
    private let viewCountIcon: UIImageView = {
        let v = UIImageView(image: UIImage(named: "comment_history_not_like"))
        return v
    }()
    private let viewCountLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#AAAAAA")
        return label
    }()
    // 收藏（bookmark）
    private let likeCountIcon: UIImageView = {
        let v = UIImageView(image: UIImage(named: "comment_history_not_saved"))
        return v
    }()
    private let likeCountLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#AAAAAA")
        return label
    }()
    private let commentCountIcon: UIImageView = {
        let v = UIImageView(image: UIImage(named: "comment_num"))
        return v
    }()
    private let commentCountLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#AAAAAA")
        return label
    }()
    // 评论区
    private let commentContainer: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 8
        return v
    }()
    private let avatarImageView: UIImageView = {
        let iv = UIImageView()
        iv.layer.cornerRadius = 14
        iv.clipsToBounds = true
        iv.backgroundColor = UIColor(hex: "#E0E0E0")
        return iv
    }()
    private let nicknameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13, weight: .medium)
        label.textColor = UIColor(hex: "#222222")
        return label
    }()
    private let commentTimeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 11)
        label.textColor = UIColor(hex: "#888888")
        return label
    }()
    private let commentContentLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13)
        label.textColor = UIColor(hex: "#444444")
        label.numberOfLines = 0
        return label
    }()
    private let commentLikeIcon: UIImageView = {
        let iv = UIImageView()
        iv.contentMode = .scaleAspectFit
        return iv
    }()
    private let commentLikeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#999999")
        return label
    }()
    private let deleteButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("删除", for: .normal)
        button.setTitleColor(UIColor(hex: "#FF3E4B"), for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 11)
        return button
    }()
    private let atReplyBarView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor(hex: "#E0E0E0")
        v.layer.cornerRadius = 1
        v.isHidden = true
        return v
    }()
    private let atReplyLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13)
        label.textColor = UIColor(hex: "#666666")
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        deleteButton.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 12, bottom: 0, right: 12))
        }
        
        // 上半部分：视频信息
        containerView.addSubview(coverImageView)
        coverImageView.snp.makeConstraints { make in
            make.left.top.equalToSuperview().offset(14)
            make.width.equalTo(48)
            make.height.equalTo(80)
        }
        
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(coverImageView)
            make.left.equalTo(coverImageView.snp.right).offset(12)
            make.right.equalToSuperview().offset(-12)
        }
        
        containerView.addSubview(tagStackView)
        tagStackView.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
        }
        
        // 统计信息
        containerView.addSubview(viewCountIcon)
        containerView.addSubview(viewCountLabel)
        containerView.addSubview(likeCountIcon)
        containerView.addSubview(likeCountLabel)
        containerView.addSubview(commentCountIcon)
        containerView.addSubview(commentCountLabel)
        
        viewCountIcon.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.bottom.equalTo(coverImageView)
            make.width.height.equalTo(24)
        }
        
        viewCountLabel.snp.makeConstraints { make in
            make.left.equalTo(viewCountIcon.snp.right).offset(4)
            make.centerY.equalTo(viewCountIcon)
        }
        
        likeCountIcon.snp.makeConstraints { make in
            make.left.equalTo(viewCountLabel.snp.right).offset(16)
            make.centerY.equalTo(viewCountIcon)
            make.width.height.equalTo(24)
        }
        
        likeCountLabel.snp.makeConstraints { make in
            make.left.equalTo(likeCountIcon.snp.right).offset(4)
            make.centerY.equalTo(viewCountIcon)
        }
        
        commentCountIcon.snp.makeConstraints { make in
            make.left.equalTo(likeCountLabel.snp.right).offset(16)
            make.centerY.equalTo(viewCountIcon)
            make.width.height.equalTo(24)
        }
        
        commentCountLabel.snp.makeConstraints { make in
            make.left.equalTo(commentCountIcon.snp.right).offset(4)
            make.centerY.equalTo(viewCountIcon)
        }
        
        // 下半部分：评论信息
        containerView.addSubview(commentContainer)
        commentContainer.snp.makeConstraints { make in
            make.top.equalTo(coverImageView.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(8)
            make.bottom.equalToSuperview().offset(-2)
        }
        
        commentContainer.addSubview(avatarImageView)
        avatarImageView.snp.makeConstraints { make in
            make.left.top.equalToSuperview().offset(12)
            make.width.height.equalTo(28)
        }
        
        commentContainer.addSubview(nicknameLabel)
        nicknameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(8)
            make.top.equalTo(avatarImageView)
        }
        
        commentContainer.addSubview(commentTimeLabel)
        commentTimeLabel.snp.makeConstraints { make in
            make.left.equalTo(nicknameLabel)
            make.top.equalTo(nicknameLabel.snp.bottom).offset(2)
        }
        
        commentContainer.addSubview(commentContentLabel)
        commentContentLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(8)
            make.left.equalTo(nicknameLabel)
            make.right.equalToSuperview().offset(-12)
        }
        commentContainer.addSubview(atReplyBarView)
        commentContainer.addSubview(atReplyLabel)
        atReplyBarView.snp.makeConstraints { make in
            make.left.equalTo(commentContentLabel)
            make.top.equalTo(commentContentLabel.snp.bottom).offset(6)
            make.width.equalTo(2.5)
            make.height.equalTo(18)
        }
        atReplyLabel.snp.makeConstraints { make in
            make.left.equalTo(atReplyBarView.snp.right).offset(6)
            make.centerY.equalTo(atReplyBarView)
            make.right.equalTo(commentContentLabel)
        }
        
        commentContainer.addSubview(commentLikeIcon)
        commentContainer.addSubview(commentLikeLabel)
        
        commentLikeIcon.snp.makeConstraints { make in
            make.left.equalTo(nicknameLabel)
            // 先默认对齐atReplyBarView，后续在configure里动态更新
            self.commentLikeIconTopConstraint = make.top.equalTo(atReplyBarView.snp.bottom).offset(8).constraint
            make.width.height.equalTo(16)
            make.bottom.equalToSuperview().offset(-8)
        }
        
        commentLikeLabel.snp.makeConstraints { make in
            make.left.equalTo(commentLikeIcon.snp.right).offset(4)
            make.centerY.equalTo(commentLikeIcon)
        }
        
        commentContainer.addSubview(deleteButton)
        deleteButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-8)
            make.centerY.equalTo(commentLikeIcon)
        }
    }
    
    // MARK: - Configuration
    func configure(with model: CommentHistoryModel) {
        self.commentId = model.commentId
        self.worksId = model.worksId
        if model.coverImage.hasPrefix("http") {
            coverImageView.kf.setImage(with: URL(string: model.coverImage),
                                       placeholder: UIImage(named: "comment_cover"))
        } else {
            coverImageView.image = UIImage(named: model.coverImage)
        }
        titleLabel.text = model.title
        
        // 标签
        tagStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        model.tags.forEach { tag in
            let tagLabel = UILabel()
            tagLabel.text = "#" + tag
            tagLabel.font = .systemFont(ofSize: 12)
            tagLabel.textColor = UIColor(hex: "#FF6236")
            tagStackView.addArrangedSubview(tagLabel)
        }
        
        // 点赞数
        viewCountLabel.text = "\(model.likes ?? 0)"
        
        // 收藏数：接口未返回，统一显示 "-"
        likeCountLabel.text = "\(model.collects ?? 0)"
        
        // 评论数
        commentCountLabel.text = "\(model.comments ?? 0)"
        
        // 评论信息（只取第一条回复或模拟）
        if let reply = model.replies.first {
            if reply.avatar.hasPrefix("http") {
                avatarImageView.kf.setImage(with: URL(string: reply.avatar),
                                            placeholder: UIImage(named: "default_avatar"))
            } else {
                avatarImageView.image = UIImage(named: reply.avatar)
            }
            nicknameLabel.text = reply.username
            commentTimeLabel.text = formatTimeString(model.time)
            commentContentLabel.text = reply.content
            commentLikeLabel.text = "\(model.likes ?? 0)" // 显示评论的点赞数

            // 新增：二级回复展示
            if let toUser = reply.replyToUser, !toUser.isEmpty {
                atReplyLabel.isHidden = false
                atReplyLabel.text = "@" + toUser
                atReplyBarView.isHidden = false
                // 二级回复，点赞按钮对齐atReplyBarView
                commentLikeIconTopConstraint?.deactivate()
                commentLikeIcon.snp.remakeConstraints { make in
                    make.left.equalTo(nicknameLabel)
                    make.top.equalTo(atReplyBarView.snp.bottom).offset(8)
                    make.width.height.equalTo(16)
                    make.bottom.equalToSuperview().offset(-8)
                }
            } else {
                atReplyLabel.isHidden = true
                atReplyLabel.text = nil
                atReplyBarView.isHidden = true
                // 一级评论，点赞按钮紧贴评论内容
                commentLikeIconTopConstraint?.deactivate()
                commentLikeIcon.snp.remakeConstraints { make in
                    make.left.equalTo(nicknameLabel)
                    make.top.equalTo(commentContentLabel.snp.bottom).offset(8)
                    make.width.height.equalTo(16)
                    make.bottom.equalToSuperview().offset(-8)
                }
            }
            let likeImageName = reply.isLiked ? "video_like_selected" : "video_like"
            commentLikeIcon.image = UIImage(named: likeImageName)
        } else {
            // 如果没有回复数据，直接显示评论内容
            avatarImageView.image = UIImage(named: "default_avatar")
            nicknameLabel.text = "我"
            commentTimeLabel.text = formatTimeString(model.time)
            commentContentLabel.text = model.comment
            commentLikeLabel.text = "\(model.likes ?? 0)"
            atReplyLabel.isHidden = true
            atReplyLabel.text = nil
            atReplyBarView.isHidden = true
            // 一级评论，点赞按钮紧贴评论内容
            commentLikeIconTopConstraint?.deactivate()
            commentLikeIcon.snp.remakeConstraints { make in
                make.left.equalTo(nicknameLabel)
                make.top.equalTo(commentContentLabel.snp.bottom).offset(8)
                make.width.height.equalTo(16)
                make.bottom.equalToSuperview().offset(-8)
            }
            commentLikeIcon.image = UIImage(named: "video_like")
        }
        
        deleteButton.isHidden = !model.deleteEnabled
    }

    // MARK: - Helper Methods
    private func formatTimeString(_ timeString: String) -> String {
        // 将 "2025-07-28 09:59:10" 格式转换为更友好的显示格式
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"

        guard let date = formatter.date(from: timeString) else {
            return timeString // 如果解析失败，返回原字符串
        }

        let now = Date()
        let timeInterval = now.timeIntervalSince(date)

        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else {
            let outputFormatter = DateFormatter()
            outputFormatter.dateFormat = "MM-dd HH:mm"
            return outputFormatter.string(from: date)
        }
    }

    @objc private func deleteButtonTapped() {
        delegate?.commentHistoryCellDidTapDelete(self, commentId: self.commentId)
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesEnded(touches, with: event)
        delegate?.commentHistoryCellDidTapCell(self, commentId: self.commentId, worksId: self.worksId)
    }
}
